{"tests/test_api/test_client.py::TestAPIClient::test_init_with_defaults": true, "tests/test_api/test_client.py::TestAPIClient::test_request_success": true, "tests/test_api/test_client.py::TestAPIClient::test_request_with_data": true, "tests/test_api/test_client.py::TestAPIClient::test_context_manager": true, "tests/test_api/test_client.py::TestAPIClient::test_close": true, "tests/test_api/test_client.py::TestAPIClient::test_request_without_signing": true, "tests/test_api/test_client.py::TestAPIClient::test_validate_response_structure": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_init_with_defaults": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_init_with_custom_algorithm": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_generate_timestamp": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_generate_nonce": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_create_signature_string": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_create_signature_string_with_special_chars": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_calculate_signature_sha256": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_calculate_signature_md5": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_calculate_signature_unsupported_algorithm": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_sign_request_data": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_sign_request_data_empty": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_sign_request_data_with_existing_signature_fields": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_verify_signature_valid": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_verify_signature_invalid": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_verify_signature_missing_fields": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_verify_signature_wrong_org_code": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_verify_signature_expired": true, "tests/test_auth/test_signature.py::TestRequestSigner::test_sign_and_verify_round_trip": true, "tests/test_api/test_task_api.py::TestTaskAPI::test_query_task_status_success": true, "tests/test_api/test_task_api.py::TestTaskAPI::test_query_batch_status_success": true, "tests/test_api/test_task_api.py::TestTaskAPI::test_query_batch_status_empty_batch_no": true, "tests/test_api/test_task_api.py::TestTaskAPI::test_query_upload_progress_success": true, "tests/test_api/test_task_api.py::TestTaskAPI::test_query_upload_progress_empty_id": true, "tests/test_api/test_task_api.py::TestTaskAPI::test_get_task_list_success": true, "tests/test_api/test_task_api.py::TestTaskAPI::test_get_task_list_with_status_filter": true, "tests/test_api/test_task_api.py::TestTaskAPI::test_get_task_list_with_task_type_filter": true, "tests/test_api/test_task_api.py::TestTaskAPI::test_retry_task_success": true, "tests/test_api/test_task_api.py::TestTaskAPI::test_retry_task_empty_id": true, "tests/test_api/test_task_api.py::TestTaskAPI::test_close_own_client": true, "tests/integration/test_end_to_end.py": true, "tests/integration/test_api_integration.py::TestAPIIntegration::test_error_handling_integration": true, "tests/integration/test_api_integration.py::TestAPIIntegration::test_configuration_integration": true, "tests/integration/test_api_integration.py::TestAPIIntegration::test_retry_mechanism_integration": true, "tests/integration/test_api_integration.py::TestAPIIntegration::test_validation_integration": true}
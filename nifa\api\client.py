"""
API基础客户端模块
提供HTTP请求的基础功能
"""

import time
import logging
from typing import Dict, Any, Optional, Union
from contextlib import contextmanager

import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

from ..config.settings import settings
from ..exceptions.base import (
    NifaAPIError,
    NifaResponseError,
    NifaTimeoutError,
    NifaNetworkError,
    create_exception_from_response
)
from ..utils.helpers import (
    parse_response,
    log_request_response,
    validate_response_structure,
    retry_on_failure
)
from ..auth.signature import RequestSigner

logger = logging.getLogger(__name__)


class APIClient:
    """API基础客户端"""
    
    def __init__(
        self,
        base_url: Optional[str] = None,
        org_code: Optional[str] = None,
        timeout: Optional[int] = None,
        max_retries: Optional[int] = None,
        retry_delay: Optional[float] = None
    ):
        """
        初始化API客户端
        
        Args:
            base_url: API基础URL
            org_code: 机构代码
            timeout: 请求超时时间
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间
        """
        self.base_url = base_url or settings.NIFA_BASE_URL
        self.org_code = org_code or settings.NIFA_ORG_CODE
        self.timeout = timeout or settings.NIFA_TIMEOUT
        self.max_retries = max_retries or settings.NIFA_MAX_RETRIES
        self.retry_delay = retry_delay or settings.NIFA_RETRY_DELAY
        
        # 创建会话
        self.session = self._create_session()
        
        # 创建签名器
        self.signer = RequestSigner(
            org_code=self.org_code,
            algorithm=settings.NIFA_SIGNATURE_ALGORITHM
        )
    
    def _create_session(self) -> requests.Session:
        """
        创建HTTP会话
        
        Returns:
            配置好的requests会话
        """
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=self.retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "OPTIONS", "POST"]
        )
        
        # 配置适配器
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=settings.CONNECTION_POOL_SIZE,
            pool_maxsize=settings.CONNECTION_POOL_MAX_SIZE
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置默认头部
        session.headers.update({
            'Content-Type': 'application/json; charset=utf-8',
            'Accept': 'application/json',
            'User-Agent': f'NIFA-Python-Client/1.0.0'
        })
        
        return session
    
    def _build_url(self, endpoint: str) -> str:
        """
        构建完整URL
        
        Args:
            endpoint: API端点
            
        Returns:
            完整URL
        """
        return f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
    
    def _validate_response(self, response: requests.Response) -> Dict[str, Any]:
        """
        验证响应
        
        Args:
            response: HTTP响应
            
        Returns:
            解析后的响应数据
            
        Raises:
            NifaAPIError: API调用失败时抛出
            NifaResponseError: 响应格式错误时抛出
        """
        # 检查HTTP状态码
        if not response.ok:
            raise NifaAPIError(
                f"HTTP请求失败: {response.status_code} {response.reason}",
                status_code=response.status_code,
                response_data={"text": response.text}
            )
        
        # 解析响应
        try:
            response_data = parse_response(response.text)
        except Exception as e:
            raise NifaResponseError(
                f"响应解析失败: {str(e)}",
                status_code=response.status_code
            ) from e
        
        # 验证响应结构
        try:
            validate_response_structure(response_data, ['code', 'message'])
        except Exception as e:
            logger.warning(f"响应结构验证失败: {str(e)}")
        
        # 检查业务状态码
        response_code = response_data.get('code', '')
        response_message = response_data.get('message', '未知错误')
        
        if response_code != '0000':  # 0000表示成功
            exception = create_exception_from_response(
                response_code,
                response_message,
                response.status_code,
                response_data
            )
            if exception:
                raise exception
        
        return response_data
    
    @contextmanager
    def _request_context(self, method: str, url: str, **kwargs):
        """
        请求上下文管理器
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 请求参数
        """
        start_time = time.time()
        request_data = kwargs.get('json') or kwargs.get('data')
        
        try:
            logger.info(f"发起{method}请求: {url}")
            yield
            
        except requests.exceptions.Timeout as e:
            duration = time.time() - start_time
            log_request_response(method, url, request_data, None, duration)
            raise NifaTimeoutError(
                f"请求超时: {str(e)}",
                timeout=self.timeout
            ) from e
            
        except requests.exceptions.ConnectionError as e:
            duration = time.time() - start_time
            log_request_response(method, url, request_data, None, duration)
            raise NifaNetworkError(
                f"网络连接错误: {str(e)}",
                original_error=e
            ) from e
            
        except requests.exceptions.RequestException as e:
            duration = time.time() - start_time
            log_request_response(method, url, request_data, None, duration)
            raise NifaNetworkError(
                f"请求异常: {str(e)}",
                original_error=e
            ) from e
        
        finally:
            duration = time.time() - start_time
            logger.info(f"{method}请求完成，耗时: {duration:.3f}秒")
    
    @retry_on_failure(
        max_retries=3,
        delay=1.0,
        exceptions=(NifaNetworkError, NifaTimeoutError)
    )
    def request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        sign_request: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """
        发起HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            params: URL参数
            headers: 请求头
            sign_request: 是否对请求进行签名
            **kwargs: 其他请求参数
            
        Returns:
            响应数据
        """
        url = self._build_url(endpoint)
        
        # 准备请求数据
        if data and sign_request:
            data = self.signer.sign_request_data(data)
        
        # 准备请求参数
        request_kwargs = {
            'timeout': self.timeout,
            'params': params,
            'headers': headers,
            **kwargs
        }
        
        if data:
            request_kwargs['json'] = data
        
        # 发起请求
        with self._request_context(method, url, **request_kwargs):
            response = self.session.request(method, url, **request_kwargs)
            
            # 验证响应
            response_data = self._validate_response(response)
            
            # 记录请求响应日志
            log_request_response(
                method,
                url,
                data,
                response_data,
                time.time() - time.time()  # 这里应该记录实际耗时
            )
            
            return response_data
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """GET请求"""
        return self.request('GET', endpoint, params=params, **kwargs)
    
    def post(self, endpoint: str, data: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """POST请求"""
        return self.request('POST', endpoint, data=data, **kwargs)
    
    def put(self, endpoint: str, data: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """PUT请求"""
        return self.request('PUT', endpoint, data=data, **kwargs)
    
    def delete(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """DELETE请求"""
        return self.request('DELETE', endpoint, **kwargs)
    
    def close(self) -> None:
        """关闭客户端"""
        if self.session:
            self.session.close()
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()

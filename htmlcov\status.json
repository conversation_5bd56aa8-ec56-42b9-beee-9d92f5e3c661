{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.1", "globals": "f9f0b06f61e8b0d035eab75b66ff97a3", "files": {"z_9589fface2250470___init___py": {"hash": "f60926621cadaf715e3ace010e761c54", "index": {"url": "z_9589fface2250470___init___py.html", "file": "nifa\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838___init___py": {"hash": "8d7d2a40d0e09174b007471272e0bda4", "index": {"url": "z_aee3eced65d61838___init___py.html", "file": "nifa\\api\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 6, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838_client_py": {"hash": "3992982ca83427e0f3f045751eeebd86", "index": {"url": "z_aee3eced65d61838_client_py.html", "file": "nifa\\api\\client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 98, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838_data_py": {"hash": "d9152279f208517706857d3c144dad1e", "index": {"url": "z_aee3eced65d61838_data_py.html", "file": "nifa\\api\\data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 120, "n_excluded": 0, "n_missing": 73, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838_info_py": {"hash": "9dc8a6c322e3f36e922654a0c0cea5a6", "index": {"url": "z_aee3eced65d61838_info_py.html", "file": "nifa\\api\\info.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 80, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838_judicial_py": {"hash": "d30db84da9e094d5eb6b213d46b3cd39", "index": {"url": "z_aee3eced65d61838_judicial_py.html", "file": "nifa\\api\\judicial.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 88, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838_query_count_py": {"hash": "4108181f565ed385b74c8313051e4976", "index": {"url": "z_aee3eced65d61838_query_count_py.html", "file": "nifa\\api\\query_count.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 0, "n_missing": 57, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_aee3eced65d61838_task_py": {"hash": "df304ddc09aedf3709ef6717eeb34544", "index": {"url": "z_aee3eced65d61838_task_py.html", "file": "nifa\\api\\task.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e30577f822d2f7f2___init___py": {"hash": "54aea83e2efc212ad13a0243fad759a0", "index": {"url": "z_e30577f822d2f7f2___init___py.html", "file": "nifa\\auth\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e30577f822d2f7f2_encryption_py": {"hash": "48042374cf913bec48c08f0662094af7", "index": {"url": "z_e30577f822d2f7f2_encryption_py.html", "file": "nifa\\auth\\encryption.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 0, "n_missing": 90, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e30577f822d2f7f2_signature_py": {"hash": "a19e7637aa62bcda823c62a492ffa691", "index": {"url": "z_e30577f822d2f7f2_signature_py.html", "file": "nifa\\auth\\signature.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 101, "n_excluded": 0, "n_missing": 40, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9f8c89c1e0a6a595___init___py": {"hash": "a54cb97746e044883578ba8ab143ce9d", "index": {"url": "z_9f8c89c1e0a6a595___init___py.html", "file": "nifa\\config\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9f8c89c1e0a6a595_settings_py": {"hash": "45fe81cbf052ff77c7466ea2157f95f9", "index": {"url": "z_9f8c89c1e0a6a595_settings_py.html", "file": "nifa\\config\\settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 103, "n_excluded": 0, "n_missing": 16, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2cbf57cf94805440___init___py": {"hash": "5261b8ec2eb0e9ba3b76f1df7fe689ed", "index": {"url": "z_2cbf57cf94805440___init___py.html", "file": "nifa\\exceptions\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_2cbf57cf94805440_base_py": {"hash": "6795b3573a0ca6f704966e8b4d93de5a", "index": {"url": "z_2cbf57cf94805440_base_py.html", "file": "nifa\\exceptions\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 72, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b14f832205fdefe___init___py": {"hash": "0b6633963b2a0b0b44dc735b8662bc64", "index": {"url": "z_1b14f832205fdefe___init___py.html", "file": "nifa\\utils\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b14f832205fdefe_helpers_py": {"hash": "b28e1bffc9023e0463ed9880c6eb812f", "index": {"url": "z_1b14f832205fdefe_helpers_py.html", "file": "nifa\\utils\\helpers.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 102, "n_excluded": 0, "n_missing": 45, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1b14f832205fdefe_validators_py": {"hash": "0d49217ba022db1d025f45350bd29fba", "index": {"url": "z_1b14f832205fdefe_validators_py.html", "file": "nifa\\utils\\validators.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 78, "n_excluded": 0, "n_missing": 43, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9f8c89c1e0a6a595_enhanced_settings_py": {"hash": "b0a8dca8a95e13c03497240e57d0fe7a", "index": {"url": "z_9f8c89c1e0a6a595_enhanced_settings_py.html", "file": "nifa\\config\\enhanced_settings.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 115, "n_excluded": 0, "n_missing": 115, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4bd54dd4baa390e5___init___py": {"hash": "60f65bdfcf2f8c616c1b6c564c967e12", "index": {"url": "z_4bd54dd4baa390e5___init___py.html", "file": "nifa\\core\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_4bd54dd4baa390e5_circuit_breaker_py": {"hash": "809541a708c9d6344740963e8558abe0", "index": {"url": "z_4bd54dd4baa390e5_circuit_breaker_py.html", "file": "nifa\\core\\circuit_breaker.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 104, "n_excluded": 0, "n_missing": 104, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b652cb6b2a49ef76___init___py": {"hash": "1e83ae6031ec9b5566466fa4d45368ef", "index": {"url": "z_b652cb6b2a49ef76___init___py.html", "file": "nifa\\models\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b652cb6b2a49ef76_base_py": {"hash": "78f0a5c5ffbf4a75d942166f4994dc43", "index": {"url": "z_b652cb6b2a49ef76_base_py.html", "file": "nifa\\models\\base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 70, "n_excluded": 0, "n_missing": 70, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}
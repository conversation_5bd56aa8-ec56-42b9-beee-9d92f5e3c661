"""
数据验证工具模块
包含各种数据验证函数
"""

import re
from typing import Optional
from datetime import datetime

from ..exceptions.base import NifaValidationError


def validate_id_card(id_card: str) -> bool:
    """
    验证身份证号码
    
    Args:
        id_card: 身份证号码
        
    Returns:
        是否有效
        
    Raises:
        NifaValidationError: 验证失败时抛出
    """
    if not id_card:
        raise NifaValidationError("身份证号码不能为空", field="id_card", value=id_card)
    
    # 去除空格
    id_card = id_card.strip()
    
    # 长度检查
    if len(id_card) not in [15, 18]:
        raise NifaValidationError(
            "身份证号码长度必须为15位或18位",
            field="id_card",
            value=id_card
        )
    
    # 15位身份证号码验证
    if len(id_card) == 15:
        if not re.match(r'^\d{15}$', id_card):
            raise NifaValidationError(
                "15位身份证号码必须全为数字",
                field="id_card",
                value=id_card
            )
        return True
    
    # 18位身份证号码验证
    if len(id_card) == 18:
        # 前17位必须为数字，最后一位可以是数字或X
        if not re.match(r'^\d{17}[\dXx]$', id_card):
            raise NifaValidationError(
                "18位身份证号码格式不正确",
                field="id_card",
                value=id_card
            )
        
        # 校验码验证
        weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
        check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
        
        sum_val = sum(int(id_card[i]) * weights[i] for i in range(17))
        check_code = check_codes[sum_val % 11]
        
        if id_card[17].upper() != check_code:
            raise NifaValidationError(
                "身份证号码校验码不正确",
                field="id_card",
                value=id_card
            )
        
        return True
    
    return False


def validate_name(name: str) -> bool:
    """
    验证姓名
    
    Args:
        name: 姓名
        
    Returns:
        是否有效
        
    Raises:
        NifaValidationError: 验证失败时抛出
    """
    if not name:
        raise NifaValidationError("姓名不能为空", field="name", value=name)
    
    name = name.strip()
    
    # 长度检查
    if len(name) < 2 or len(name) > 50:
        raise NifaValidationError(
            "姓名长度必须在2-50个字符之间",
            field="name",
            value=name
        )
    
    # 字符检查：只允许中文、英文字母、点号、中点
    if not re.match(r'^[\u4e00-\u9fa5a-zA-Z·.]+$', name):
        raise NifaValidationError(
            "姓名只能包含中文、英文字母、点号和中点",
            field="name",
            value=name
        )
    
    return True


def validate_phone(phone: str) -> bool:
    """
    验证手机号码
    
    Args:
        phone: 手机号码
        
    Returns:
        是否有效
        
    Raises:
        NifaValidationError: 验证失败时抛出
    """
    if not phone:
        raise NifaValidationError("手机号码不能为空", field="phone", value=phone)
    
    phone = phone.strip()
    
    # 中国大陆手机号码验证
    if not re.match(r'^1[3-9]\d{9}$', phone):
        raise NifaValidationError(
            "手机号码格式不正确",
            field="phone",
            value=phone
        )
    
    return True


def validate_org_code(org_code: str) -> bool:
    """
    验证机构代码
    
    Args:
        org_code: 机构代码
        
    Returns:
        是否有效
        
    Raises:
        NifaValidationError: 验证失败时抛出
    """
    if not org_code:
        raise NifaValidationError("机构代码不能为空", field="org_code", value=org_code)
    
    org_code = org_code.strip()
    
    # 机构代码通常为数字和字母组合，长度8-20位
    if not re.match(r'^[A-Za-z0-9]{8,20}$', org_code):
        raise NifaValidationError(
            "机构代码格式不正确，应为8-20位数字和字母组合",
            field="org_code",
            value=org_code
        )
    
    return True


def validate_date_string(date_str: str, format_str: str = "%Y-%m-%d") -> bool:
    """
    验证日期字符串
    
    Args:
        date_str: 日期字符串
        format_str: 日期格式
        
    Returns:
        是否有效
        
    Raises:
        NifaValidationError: 验证失败时抛出
    """
    if not date_str:
        raise NifaValidationError("日期不能为空", field="date", value=date_str)
    
    try:
        datetime.strptime(date_str, format_str)
        return True
    except ValueError as e:
        raise NifaValidationError(
            f"日期格式不正确，期望格式：{format_str}",
            field="date",
            value=date_str
        ) from e


def validate_amount(amount: str) -> bool:
    """
    验证金额
    
    Args:
        amount: 金额字符串
        
    Returns:
        是否有效
        
    Raises:
        NifaValidationError: 验证失败时抛出
    """
    if not amount:
        raise NifaValidationError("金额不能为空", field="amount", value=amount)
    
    # 金额格式：支持整数和小数，最多2位小数
    if not re.match(r'^\d+(\.\d{1,2})?$', amount):
        raise NifaValidationError(
            "金额格式不正确，应为数字，最多2位小数",
            field="amount",
            value=amount
        )
    
    # 金额范围检查
    try:
        amount_float = float(amount)
        if amount_float < 0:
            raise NifaValidationError(
                "金额不能为负数",
                field="amount",
                value=amount
            )
        if amount_float > 999999999.99:
            raise NifaValidationError(
                "金额不能超过999999999.99",
                field="amount",
                value=amount
            )
    except ValueError as e:
        raise NifaValidationError(
            "金额格式不正确",
            field="amount",
            value=amount
        ) from e
    
    return True


def validate_required_fields(data: dict, required_fields: list) -> bool:
    """
    验证必填字段
    
    Args:
        data: 数据字典
        required_fields: 必填字段列表
        
    Returns:
        是否有效
        
    Raises:
        NifaValidationError: 验证失败时抛出
    """
    missing_fields = []
    
    for field in required_fields:
        if field not in data or data[field] is None or str(data[field]).strip() == "":
            missing_fields.append(field)
    
    if missing_fields:
        raise NifaValidationError(
            f"缺少必填字段: {', '.join(missing_fields)}",
            field="required_fields",
            value=missing_fields
        )
    
    return True

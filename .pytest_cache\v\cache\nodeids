["tests/test_api/test_info_api.py::TestInfoAPI::test_batch_query_personal_credit_empty_list", "tests/test_api/test_info_api.py::TestInfoAPI::test_batch_query_personal_credit_invalid_item", "tests/test_api/test_info_api.py::TestInfoAPI::test_batch_query_personal_credit_success", "tests/test_api/test_info_api.py::TestInfoAPI::test_batch_query_personal_credit_too_many_items", "tests/test_api/test_info_api.py::TestInfoAPI::test_close_external_client", "tests/test_api/test_info_api.py::TestInfoAPI::test_close_own_client", "tests/test_api/test_info_api.py::TestInfoAPI::test_context_manager", "tests/test_api/test_info_api.py::TestInfoAPI::test_get_query_history_invalid_date", "tests/test_api/test_info_api.py::TestInfoAPI::test_get_query_history_invalid_page", "tests/test_api/test_info_api.py::TestInfoAPI::test_get_query_history_success", "tests/test_api/test_info_api.py::TestInfoAPI::test_init_with_client", "tests/test_api/test_info_api.py::TestInfoAPI::test_init_without_client", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_credit_report_invalid_report_id", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_credit_report_invalid_report_type", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_credit_report_success", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_enterprise_credit_invalid_org_code", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_enterprise_credit_success", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_personal_credit_invalid_id_card", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_personal_credit_invalid_name", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_personal_credit_invalid_query_type", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_personal_credit_success", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_personal_credit_with_custom_params", "tests/test_config/test_settings.py::TestEnvironment::test_environment_comparison", "tests/test_config/test_settings.py::TestEnvironment::test_environment_values", "tests/test_config/test_settings.py::TestSettings::test_base_url_validation", "tests/test_config/test_settings.py::TestSettings::test_connection_pool_validation", "tests/test_config/test_settings.py::TestSettings::test_debug_mode_in_production", "tests/test_config/test_settings.py::TestSettings::test_default_settings", "tests/test_config/test_settings.py::TestSettings::test_environment_helpers", "tests/test_config/test_settings.py::TestSettings::test_environment_validation", "tests/test_config/test_settings.py::TestSettings::test_environment_variable_loading", "tests/test_config/test_settings.py::TestSettings::test_get_api_url", "tests/test_config/test_settings.py::TestSettings::test_log_config", "tests/test_config/test_settings.py::TestSettings::test_log_level_validation", "tests/test_config/test_settings.py::TestSettings::test_retry_validation", "tests/test_config/test_settings.py::TestSettings::test_timeout_validation", "tests/test_utils/test_validators.py::TestValidateAmount::test_amount_range", "tests/test_utils/test_validators.py::TestValidateAmount::test_empty_amount", "tests/test_utils/test_validators.py::TestValidateAmount::test_invalid_amount_format", "tests/test_utils/test_validators.py::TestValidateAmount::test_valid_amounts", "tests/test_utils/test_validators.py::TestValidateDateString::test_empty_date_string", "tests/test_utils/test_validators.py::TestValidateDateString::test_invalid_date_format", "tests/test_utils/test_validators.py::TestValidateDateString::test_valid_date_strings", "tests/test_utils/test_validators.py::TestValidateIdCard::test_empty_id_card", "tests/test_utils/test_validators.py::TestValidateIdCard::test_invalid_check_code", "tests/test_utils/test_validators.py::TestValidateIdCard::test_invalid_format_15_digit", "tests/test_utils/test_validators.py::TestValidateIdCard::test_invalid_format_18_digit", "tests/test_utils/test_validators.py::TestValidateIdCard::test_invalid_length_id_card", "tests/test_utils/test_validators.py::TestValidateIdCard::test_valid_15_digit_id_card", "tests/test_utils/test_validators.py::TestValidateIdCard::test_valid_18_digit_id_card", "tests/test_utils/test_validators.py::TestValidateName::test_empty_name", "tests/test_utils/test_validators.py::TestValidateName::test_invalid_characters", "tests/test_utils/test_validators.py::TestValidateName::test_too_long_name", "tests/test_utils/test_validators.py::TestValidateName::test_too_short_name", "tests/test_utils/test_validators.py::TestValidateName::test_valid_chinese_name", "tests/test_utils/test_validators.py::TestValidateName::test_valid_english_name", "tests/test_utils/test_validators.py::TestValidateName::test_valid_mixed_name", "tests/test_utils/test_validators.py::TestValidateOrgCode::test_empty_org_code", "tests/test_utils/test_validators.py::TestValidateOrgCode::test_invalid_org_code_format", "tests/test_utils/test_validators.py::TestValidateOrgCode::test_valid_org_codes", "tests/test_utils/test_validators.py::TestValidatePhone::test_empty_phone", "tests/test_utils/test_validators.py::TestValidatePhone::test_invalid_phone_format", "tests/test_utils/test_validators.py::TestValidatePhone::test_valid_phone_numbers", "tests/test_utils/test_validators.py::TestValidateRequiredFields::test_empty_required_field_values", "tests/test_utils/test_validators.py::TestValidateRequiredFields::test_missing_required_fields", "tests/test_utils/test_validators.py::TestValidateRequiredFields::test_valid_required_fields"]
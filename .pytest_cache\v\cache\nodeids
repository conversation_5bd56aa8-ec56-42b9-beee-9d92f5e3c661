["tests/integration/test_api_integration.py::TestAPIIntegration::test_batch_operations_integration", "tests/integration/test_api_integration.py::TestAPIIntegration::test_configuration_integration", "tests/integration/test_api_integration.py::TestAPIIntegration::test_context_manager_integration", "tests/integration/test_api_integration.py::TestAPIIntegration::test_data_api_with_encryption_integration", "tests/integration/test_api_integration.py::TestAPIIntegration::test_error_handling_integration", "tests/integration/test_api_integration.py::TestAPIIntegration::test_file_upload_integration", "tests/integration/test_api_integration.py::TestAPIIntegration::test_info_api_with_real_client", "tests/integration/test_api_integration.py::TestAPIIntegration::test_logging_integration", "tests/integration/test_api_integration.py::TestAPIIntegration::test_retry_mechanism_integration", "tests/integration/test_api_integration.py::TestAPIIntegration::test_signature_integration_with_api_client", "tests/integration/test_api_integration.py::TestAPIIntegration::test_task_api_status_query_integration", "tests/integration/test_api_integration.py::TestAPIIntegration::test_validation_integration", "tests/test_api/test_client.py::TestAPIClient::test_build_url", "tests/test_api/test_client.py::TestAPIClient::test_close", "tests/test_api/test_client.py::TestAPIClient::test_context_manager", "tests/test_api/test_client.py::TestAPIClient::test_create_session", "tests/test_api/test_client.py::TestAPIClient::test_get_method", "tests/test_api/test_client.py::TestAPIClient::test_init_with_custom_params", "tests/test_api/test_client.py::TestAPIClient::test_init_with_defaults", "tests/test_api/test_client.py::TestAPIClient::test_post_method", "tests/test_api/test_client.py::TestAPIClient::test_request_connection_error", "tests/test_api/test_client.py::TestAPIClient::test_request_context_manager", "tests/test_api/test_client.py::TestAPIClient::test_request_http_error", "tests/test_api/test_client.py::TestAPIClient::test_request_invalid_json", "tests/test_api/test_client.py::TestAPIClient::test_request_success", "tests/test_api/test_client.py::TestAPIClient::test_request_timeout", "tests/test_api/test_client.py::TestAPIClient::test_request_with_data", "tests/test_api/test_client.py::TestAPIClient::test_request_without_signing", "tests/test_api/test_client.py::TestAPIClient::test_validate_response_structure", "tests/test_api/test_data_api.py::TestDataAPI::test_batch_upload_data_empty_list", "tests/test_api/test_data_api.py::TestDataAPI::test_batch_upload_data_invalid_item", "tests/test_api/test_data_api.py::TestDataAPI::test_batch_upload_data_success", "tests/test_api/test_data_api.py::TestDataAPI::test_batch_upload_data_too_many_items", "tests/test_api/test_data_api.py::TestDataAPI::test_close_external_client", "tests/test_api/test_data_api.py::TestDataAPI::test_close_own_client", "tests/test_api/test_data_api.py::TestDataAPI::test_context_manager", "tests/test_api/test_data_api.py::TestDataAPI::test_init_with_client", "tests/test_api/test_data_api.py::TestDataAPI::test_init_without_client", "tests/test_api/test_data_api.py::TestDataAPI::test_upload_data_dict_content", "tests/test_api/test_data_api.py::TestDataAPI::test_upload_data_empty_content", "tests/test_api/test_data_api.py::TestDataAPI::test_upload_data_invalid_type", "tests/test_api/test_data_api.py::TestDataAPI::test_upload_data_list_content", "tests/test_api/test_data_api.py::TestDataAPI::test_upload_data_success", "tests/test_api/test_data_api.py::TestDataAPI::test_upload_data_with_batch_no", "tests/test_api/test_data_api.py::TestDataAPI::test_upload_data_with_encryption", "tests/test_api/test_data_api.py::TestDataAPI::test_upload_file_empty_path", "tests/test_api/test_data_api.py::TestDataAPI::test_upload_file_empty_type", "tests/test_api/test_data_api.py::TestDataAPI::test_upload_file_not_exists", "tests/test_api/test_data_api.py::TestDataAPI::test_upload_file_success", "tests/test_api/test_data_api.py::TestDataAPI::test_upload_file_with_encryption", "tests/test_api/test_info_api.py::TestInfoAPI::test_batch_query_personal_credit_empty_list", "tests/test_api/test_info_api.py::TestInfoAPI::test_batch_query_personal_credit_invalid_item", "tests/test_api/test_info_api.py::TestInfoAPI::test_batch_query_personal_credit_success", "tests/test_api/test_info_api.py::TestInfoAPI::test_batch_query_personal_credit_too_many_items", "tests/test_api/test_info_api.py::TestInfoAPI::test_close_external_client", "tests/test_api/test_info_api.py::TestInfoAPI::test_close_own_client", "tests/test_api/test_info_api.py::TestInfoAPI::test_context_manager", "tests/test_api/test_info_api.py::TestInfoAPI::test_get_query_history_invalid_date", "tests/test_api/test_info_api.py::TestInfoAPI::test_get_query_history_invalid_page", "tests/test_api/test_info_api.py::TestInfoAPI::test_get_query_history_success", "tests/test_api/test_info_api.py::TestInfoAPI::test_init_with_client", "tests/test_api/test_info_api.py::TestInfoAPI::test_init_without_client", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_credit_report_invalid_report_id", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_credit_report_invalid_report_type", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_credit_report_success", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_enterprise_credit_invalid_org_code", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_enterprise_credit_success", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_personal_credit_invalid_id_card", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_personal_credit_invalid_name", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_personal_credit_invalid_query_type", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_personal_credit_success", "tests/test_api/test_info_api.py::TestInfoAPI::test_query_personal_credit_with_custom_params", "tests/test_api/test_task_api.py::TestTaskAPI::test_cancel_task_empty_id", "tests/test_api/test_task_api.py::TestTaskAPI::test_cancel_task_success", "tests/test_api/test_task_api.py::TestTaskAPI::test_close_external_client", "tests/test_api/test_task_api.py::TestTaskAPI::test_close_own_client", "tests/test_api/test_task_api.py::TestTaskAPI::test_context_manager", "tests/test_api/test_task_api.py::TestTaskAPI::test_get_task_list_invalid_date_format", "tests/test_api/test_task_api.py::TestTaskAPI::test_get_task_list_invalid_page", "tests/test_api/test_task_api.py::TestTaskAPI::test_get_task_list_invalid_page_size", "tests/test_api/test_task_api.py::TestTaskAPI::test_get_task_list_page_size_too_large", "tests/test_api/test_task_api.py::TestTaskAPI::test_get_task_list_success", "tests/test_api/test_task_api.py::TestTaskAPI::test_get_task_list_with_status_filter", "tests/test_api/test_task_api.py::TestTaskAPI::test_get_task_list_with_task_type_filter", "tests/test_api/test_task_api.py::TestTaskAPI::test_init_with_client", "tests/test_api/test_task_api.py::TestTaskAPI::test_init_without_client", "tests/test_api/test_task_api.py::TestTaskAPI::test_query_batch_status_empty_batch_no", "tests/test_api/test_task_api.py::TestTaskAPI::test_query_batch_status_success", "tests/test_api/test_task_api.py::TestTaskAPI::test_query_task_status_empty_id", "tests/test_api/test_task_api.py::TestTaskAPI::test_query_task_status_none_id", "tests/test_api/test_task_api.py::TestTaskAPI::test_query_task_status_success", "tests/test_api/test_task_api.py::TestTaskAPI::test_query_upload_progress_empty_id", "tests/test_api/test_task_api.py::TestTaskAPI::test_query_upload_progress_success", "tests/test_api/test_task_api.py::TestTaskAPI::test_retry_task_empty_id", "tests/test_api/test_task_api.py::TestTaskAPI::test_retry_task_success", "tests/test_auth/test_signature.py::TestRequestSigner::test_calculate_signature_md5", "tests/test_auth/test_signature.py::TestRequestSigner::test_calculate_signature_sha256", "tests/test_auth/test_signature.py::TestRequestSigner::test_calculate_signature_unsupported_algorithm", "tests/test_auth/test_signature.py::TestRequestSigner::test_create_signature_string", "tests/test_auth/test_signature.py::TestRequestSigner::test_create_signature_string_with_special_chars", "tests/test_auth/test_signature.py::TestRequestSigner::test_generate_nonce", "tests/test_auth/test_signature.py::TestRequestSigner::test_generate_timestamp", "tests/test_auth/test_signature.py::TestRequestSigner::test_init_with_custom_algorithm", "tests/test_auth/test_signature.py::TestRequestSigner::test_init_with_defaults", "tests/test_auth/test_signature.py::TestRequestSigner::test_sign_and_verify_round_trip", "tests/test_auth/test_signature.py::TestRequestSigner::test_sign_request_data", "tests/test_auth/test_signature.py::TestRequestSigner::test_sign_request_data_empty", "tests/test_auth/test_signature.py::TestRequestSigner::test_sign_request_data_with_existing_signature_fields", "tests/test_auth/test_signature.py::TestRequestSigner::test_verify_signature_expired", "tests/test_auth/test_signature.py::TestRequestSigner::test_verify_signature_invalid", "tests/test_auth/test_signature.py::TestRequestSigner::test_verify_signature_missing_fields", "tests/test_auth/test_signature.py::TestRequestSigner::test_verify_signature_valid", "tests/test_auth/test_signature.py::TestRequestSigner::test_verify_signature_wrong_org_code", "tests/test_config/test_settings.py::TestEnvironment::test_environment_comparison", "tests/test_config/test_settings.py::TestEnvironment::test_environment_values", "tests/test_config/test_settings.py::TestSettings::test_base_url_validation", "tests/test_config/test_settings.py::TestSettings::test_connection_pool_validation", "tests/test_config/test_settings.py::TestSettings::test_debug_mode_in_production", "tests/test_config/test_settings.py::TestSettings::test_default_settings", "tests/test_config/test_settings.py::TestSettings::test_environment_helpers", "tests/test_config/test_settings.py::TestSettings::test_environment_validation", "tests/test_config/test_settings.py::TestSettings::test_environment_variable_loading", "tests/test_config/test_settings.py::TestSettings::test_get_api_url", "tests/test_config/test_settings.py::TestSettings::test_log_config", "tests/test_config/test_settings.py::TestSettings::test_log_level_validation", "tests/test_config/test_settings.py::TestSettings::test_retry_validation", "tests/test_config/test_settings.py::TestSettings::test_timeout_validation", "tests/test_utils/test_helpers.py::TestCalculateHash::test_calculate_md5_bytes", "tests/test_utils/test_helpers.py::TestCalculateHash::test_calculate_md5_string", "tests/test_utils/test_helpers.py::TestCalculateHash::test_calculate_sha256_bytes", "tests/test_utils/test_helpers.py::TestCalculateHash::test_calculate_sha256_string", "tests/test_utils/test_helpers.py::TestCaseConversion::test_convert_to_camel_case", "tests/test_utils/test_helpers.py::TestCaseConversion::test_convert_to_snake_case", "tests/test_utils/test_helpers.py::TestFormatDatetime::test_format_datetime_custom_format", "tests/test_utils/test_helpers.py::TestFormatDatetime::test_format_datetime_default", "tests/test_utils/test_helpers.py::TestFormatDatetime::test_format_datetime_none", "tests/test_utils/test_helpers.py::TestGenerateRequestId::test_generate_request_id_format", "tests/test_utils/test_helpers.py::TestGenerateRequestId::test_generate_request_id_uniqueness", "tests/test_utils/test_helpers.py::TestLogRequestResponse::test_log_request_response_success", "tests/test_utils/test_helpers.py::TestLogRequestResponse::test_log_request_response_with_sensitive_data", "tests/test_utils/test_helpers.py::TestMaskSensitiveData::test_mask_custom_char", "tests/test_utils/test_helpers.py::TestMaskSensitiveData::test_mask_custom_keep_length", "tests/test_utils/test_helpers.py::TestMaskSensitiveData::test_mask_default_settings", "tests/test_utils/test_helpers.py::TestMaskSensitiveData::test_mask_empty_string", "tests/test_utils/test_helpers.py::TestMaskSensitiveData::test_mask_id_card_format", "tests/test_utils/test_helpers.py::TestMaskSensitiveData::test_mask_no_end_keep", "tests/test_utils/test_helpers.py::TestMaskSensitiveData::test_mask_phone_format", "tests/test_utils/test_helpers.py::TestMaskSensitiveData::test_mask_short_data", "tests/test_utils/test_helpers.py::TestParseResponse::test_parse_complex_json", "tests/test_utils/test_helpers.py::TestParseResponse::test_parse_empty_string", "tests/test_utils/test_helpers.py::TestParseResponse::test_parse_invalid_json_string", "tests/test_utils/test_helpers.py::TestParseResponse::test_parse_valid_json_string", "tests/test_utils/test_helpers.py::TestRetryOnFailure::test_retry_max_attempts_exceeded", "tests/test_utils/test_helpers.py::TestRetryOnFailure::test_retry_success_after_failures", "tests/test_utils/test_helpers.py::TestRetryOnFailure::test_retry_success_on_first_attempt", "tests/test_utils/test_helpers.py::TestRetryOnFailure::test_retry_with_specific_exceptions", "tests/test_utils/test_helpers.py::TestSafeGet::test_safe_get_existing_key", "tests/test_utils/test_helpers.py::TestSafeGet::test_safe_get_nested_key", "tests/test_utils/test_helpers.py::TestSafeGet::test_safe_get_nonexistent_key", "tests/test_utils/test_helpers.py::TestSafeGet::test_safe_get_with_default", "tests/test_utils/test_helpers.py::TestValidateResponseStructure::test_validate_empty_required_fields", "tests/test_utils/test_helpers.py::TestValidateResponseStructure::test_validate_minimal_structure", "tests/test_utils/test_helpers.py::TestValidateResponseStructure::test_validate_missing_fields", "tests/test_utils/test_helpers.py::TestValidateResponseStructure::test_validate_valid_structure", "tests/test_utils/test_validators.py::TestValidateAmount::test_amount_range", "tests/test_utils/test_validators.py::TestValidateAmount::test_empty_amount", "tests/test_utils/test_validators.py::TestValidateAmount::test_invalid_amount_format", "tests/test_utils/test_validators.py::TestValidateAmount::test_valid_amounts", "tests/test_utils/test_validators.py::TestValidateDateString::test_empty_date_string", "tests/test_utils/test_validators.py::TestValidateDateString::test_invalid_date_format", "tests/test_utils/test_validators.py::TestValidateDateString::test_valid_date_strings", "tests/test_utils/test_validators.py::TestValidateIdCard::test_empty_id_card", "tests/test_utils/test_validators.py::TestValidateIdCard::test_invalid_check_code", "tests/test_utils/test_validators.py::TestValidateIdCard::test_invalid_format_15_digit", "tests/test_utils/test_validators.py::TestValidateIdCard::test_invalid_format_18_digit", "tests/test_utils/test_validators.py::TestValidateIdCard::test_invalid_length_id_card", "tests/test_utils/test_validators.py::TestValidateIdCard::test_valid_15_digit_id_card", "tests/test_utils/test_validators.py::TestValidateIdCard::test_valid_18_digit_id_card", "tests/test_utils/test_validators.py::TestValidateName::test_empty_name", "tests/test_utils/test_validators.py::TestValidateName::test_invalid_characters", "tests/test_utils/test_validators.py::TestValidateName::test_too_long_name", "tests/test_utils/test_validators.py::TestValidateName::test_too_short_name", "tests/test_utils/test_validators.py::TestValidateName::test_valid_chinese_name", "tests/test_utils/test_validators.py::TestValidateName::test_valid_english_name", "tests/test_utils/test_validators.py::TestValidateName::test_valid_mixed_name", "tests/test_utils/test_validators.py::TestValidateOrgCode::test_empty_org_code", "tests/test_utils/test_validators.py::TestValidateOrgCode::test_invalid_org_code_format", "tests/test_utils/test_validators.py::TestValidateOrgCode::test_valid_org_codes", "tests/test_utils/test_validators.py::TestValidatePhone::test_empty_phone", "tests/test_utils/test_validators.py::TestValidatePhone::test_invalid_phone_format", "tests/test_utils/test_validators.py::TestValidatePhone::test_valid_phone_numbers", "tests/test_utils/test_validators.py::TestValidateRequiredFields::test_empty_required_field_values", "tests/test_utils/test_validators.py::TestValidateRequiredFields::test_missing_required_fields", "tests/test_utils/test_validators.py::TestValidateRequiredFields::test_valid_required_fields"]